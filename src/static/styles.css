/* Modern CSS Reset and Variables */
:root {
    /* Color Palette - Dark Theme with Trending Colors */
    --bg-primary: #0a0a0f;
    --bg-secondary: #12121a;
    --bg-tertiary: #1a1a26;
    --bg-card: #1e1e2e;
    --bg-hover: #252538;
    
    /* Accent Colors */
    --accent-primary: #6366f1;
    --accent-secondary: #8b5cf6;
    --accent-tertiary: #06b6d4;
    --accent-success: #10b981;
    --accent-warning: #f59e0b;
    --accent-error: #ef4444;
    
    /* Text Colors */
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    --text-disabled: #475569;
    
    /* Border Colors */
    --border-primary: #334155;
    --border-secondary: #1e293b;
    --border-accent: #6366f1;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #6366f1, #8b5cf6);
    --gradient-secondary: linear-gradient(135deg, #06b6d4, #3b82f6);
    --gradient-accent: linear-gradient(135deg, #8b5cf6, #ec4899);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-glow: 0 0 20px rgba(99, 102, 241, 0.3);
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
    
    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* Reset */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Base Styles */
html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
}

/* Header */
.app-header {
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-primary);
    padding: var(--space-md) var(--space-xl);
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(10px);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-md);
}

.header-brand {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.logo i {
    color: var(--accent-primary);
    font-size: 1.75rem;
}

.logo-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tagline {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 400;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    border: none;
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.5;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md), var(--shadow-glow);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg), 0 0 30px rgba(99, 102, 241, 0.4);
}

.btn-outline {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--border-primary);
}

.btn-outline:hover:not(:disabled) {
    background: var(--bg-hover);
    color: var(--text-primary);
    border-color: var(--accent-primary);
}

.btn-large {
    padding: var(--space-lg) var(--space-2xl);
    font-size: 1rem;
    font-weight: 600;
}

.btn-sm {
    padding: var(--space-xs) var(--space-md);
    font-size: 0.75rem;
}

/* Form Controls */
.form-control {
    width: 100%;
    padding: var(--space-sm) var(--space-md);
    background: var(--bg-tertiary);
    border: 1px solid var(--border-secondary);
    border-radius: var(--radius-sm);
    color: var(--text-primary);
    font-family: inherit;
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-range {
    width: 100%;
    height: 6px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    outline: none;
    appearance: none;
    -webkit-appearance: none;
    cursor: pointer;
}

.form-range::-webkit-slider-thumb {
    appearance: none;
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: var(--gradient-primary);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
}

.form-range::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-lg), var(--shadow-glow);
}

.form-range::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: var(--gradient-primary);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: var(--shadow-md);
}

/* Main Content */
.main-content {
    flex: 1;
    background: var(--bg-primary);
    position: relative;
}

/* Upload Section Styles */
.upload-section {
    padding: var(--space-2xl);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: calc(100vh - 200px);
}

.upload-container {
    max-width: 1200px;
    width: 100%;
    text-align: center;
}

.upload-hero {
    margin-bottom: var(--space-2xl);
}

.upload-area {
    background: var(--bg-card);
    border: 2px dashed var(--border-primary);
    border-radius: var(--radius-xl);
    padding: var(--space-2xl);
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.upload-area:hover {
    border-color: var(--accent-primary);
    background: var(--bg-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.upload-area.drag-over {
    border-color: var(--accent-primary);
    background: rgba(99, 102, 241, 0.1);
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.2);
}

.upload-icon {
    font-size: 4rem;
    color: var(--accent-primary);
    margin-bottom: var(--space-lg);
    display: block;
}

.upload-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.upload-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: var(--space-xl);
    line-height: 1.6;
}

.file-info {
    margin-top: var(--space-lg);
    padding: var(--space-md);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.upload-limits {
    display: flex;
    justify-content: center;
    gap: var(--space-xl);
    margin-top: var(--space-lg);
    font-size: 0.875rem;
    color: var(--text-muted);
}

.upload-limits span {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-xl);
    margin-top: var(--space-2xl);
}

.feature-card {
    background: var(--bg-card);
    border: 1px solid var(--border-secondary);
    border-radius: var(--radius-lg);
    padding: var(--space-xl);
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--accent-primary);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-icon {
    font-size: 2.5rem;
    color: var(--accent-primary);
    margin-bottom: var(--space-lg);
}

.feature-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--space-md);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Progress Indicator */
.progress-container {
    margin-top: var(--space-xl);
    padding: var(--space-lg);
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-secondary);
}

.progress-wrapper {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-sm);
    transition: width var(--transition-normal);
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.progress-text {
    color: var(--text-secondary);
}

.progress-percentage {
    color: var(--accent-primary);
    font-weight: 600;
}

/* Editor Section */
.editor-section {
    padding: var(--space-xl);
    display: flex;
    flex-direction: column;
    gap: var(--space-xl);
    min-height: calc(100vh - 120px);
}

.editor-layout {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--space-xl);
    height: 100%;
}

/* Video Area */
.video-area {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.video-container {
    position: relative;
    background: #000;
    border-radius: var(--radius-lg);
    overflow: hidden;
    aspect-ratio: 16/9;
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
}

#videoPlayer {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.caption-overlay {
    position: absolute;
    bottom: 10%;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    font-size: 24px;
    font-family: Arial, sans-serif;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    padding: 10px 20px;
    background: rgba(0,0,0,0.7);
    border-radius: var(--radius-md);
    max-width: 80%;
    word-wrap: break-word;
    pointer-events: none;
    z-index: 10;
}

/* Timeline Section */
.timeline-section {
    background: var(--bg-card);
    border: 1px solid var(--border-secondary);
    border-radius: var(--radius-lg);
    padding: var(--space-lg);
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-lg);
    padding-bottom: var(--space-md);
    border-bottom: 1px solid var(--border-secondary);
}

.timeline-header h3 {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.timeline-controls {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.word-grouping-toggle {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    margin-right: var(--space-sm);
}

.toggle-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    white-space: nowrap;
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    transition: var(--transition-fast);
    border-radius: 20px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 14px;
    width: 14px;
    left: 2px;
    bottom: 2px;
    background-color: var(--text-muted);
    transition: var(--transition-fast);
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: var(--accent-primary);
    border-color: var(--accent-primary);
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
    background-color: white;
}

.toggle-slider:hover {
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

.timeline-container {
    position: relative;
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
    min-height: 80px;
    overflow-x: hidden; /* Will be set to auto when zoomed */
    overflow-y: hidden;
    user-select: none; /* Prevent text selection during drag */
}

/* Custom scrollbar styling for timeline */
.timeline-container::-webkit-scrollbar {
    height: 8px;
}

.timeline-container::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

.timeline-container::-webkit-scrollbar-thumb {
    background: var(--accent-primary);
    border-radius: 4px;
    opacity: 0.7;
}

.timeline-container::-webkit-scrollbar-thumb:hover {
    background: var(--accent-secondary);
    opacity: 1;
}

.timeline-ruler {
    height: 20px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-secondary);
    position: relative;
}

.timeline-track {
    height: 60px;
    position: relative;
    padding: var(--space-sm);
    min-width: 100%; /* Ensure minimum width */
}

.timeline-playhead {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--accent-primary);
    z-index: 10;
    pointer-events: none;
    box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
}

/* Control Panels */
.control-panels {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.control-panel {
    background: var(--bg-card);
    border: 1px solid var(--border-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--space-lg);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.panel-header:hover {
    background: var(--bg-hover);
}

.panel-header h3 {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.panel-toggle {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.panel-toggle:hover {
    background: var(--bg-hover);
    color: var(--text-primary);
}

.panel-content {
    padding: var(--space-lg);
}

.control-grid {
    display: flex;
    flex-direction: column;
    gap: var(--space-lg);
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.control-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--text-secondary);
}

.range-control {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.range-value {
    min-width: 3rem;
    text-align: right;
    font-size: 0.875rem;
    color: var(--text-muted);
    font-family: var(--font-mono);
}

.color-controls {
    display: flex;
    align-items: center;
    gap: var(--space-lg);
}

.color-input {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.color-input label {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.color-input input[type="color"] {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    background: none;
}

.checkbox-input {
    display: flex;
    align-items: center;
}

.checkbox-input label {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    cursor: pointer;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.checkbox-input input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all var(--transition-fast);
}

.checkbox-input input[type="checkbox"]:checked + .checkmark {
    background: var(--gradient-primary);
    border-color: var(--accent-primary);
}

.checkbox-input input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
}

.position-controls {
    display: flex;
    flex-direction: column;
    gap: var(--space-md);
}

.position-input {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.position-input label {
    font-size: 0.75rem;
    color: var(--text-muted);
}

/* Transcript Styles */
.transcript-content {
    max-height: 300px;
    overflow-y: auto;
    padding: var(--space-md);
    background: var(--bg-tertiary);
    border-radius: var(--radius-md);
}

.transcript-placeholder {
    color: var(--text-muted);
    font-style: italic;
    text-align: center;
    padding: var(--space-xl);
}

/* Status Bar */
.status-bar {
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-primary);
    padding: var(--space-md) var(--space-xl);
    font-size: 0.875rem;
}

.status-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.status-indicator i {
    color: var(--accent-success);
    font-size: 0.75rem;
}

#statusText {
    color: var(--text-secondary);
}

.status-info {
    color: var(--text-muted);
    font-size: 0.75rem;
}

/* Word Blocks (Timeline) */
.word-block {
    position: absolute;
    background: var(--gradient-primary);
    color: white;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    cursor: pointer;
    user-select: none;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
}

.word-block:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.word-block.selected {
    background: var(--gradient-accent);
    box-shadow: var(--shadow-lg);
}

.word-block.grouped-word {
    background: linear-gradient(135deg, var(--accent-secondary), var(--accent-tertiary));
    border: 1px solid rgba(139, 92, 246, 0.3);
    font-weight: 500;
}

.word-block.grouped-word:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
}

.word-block.grouped-word.selected {
    background: linear-gradient(135deg, var(--accent-warning), var(--accent-error));
    border-color: var(--accent-warning);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .editor-layout {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }

    .control-panels {
        flex-direction: row;
        overflow-x: auto;
    }

    .control-panel {
        min-width: 300px;
    }
}

@media (max-width: 768px) {
    .app-header {
        padding: var(--space-sm) var(--space-md);
    }

    .header-brand {
        gap: var(--space-md);
    }

    .logo {
        font-size: 1.25rem;
    }

    .tagline {
        display: none;
    }

    .upload-section {
        padding: var(--space-lg);
    }

    .upload-title {
        font-size: 1.5rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-lg);
    }

    .editor-section {
        padding: var(--space-lg);
    }

    .control-panels {
        flex-direction: column;
    }

    .control-panel {
        min-width: auto;
    }

    .color-controls {
        flex-direction: column;
        align-items: stretch;
        gap: var(--space-md);
    }

    .upload-limits {
        flex-direction: column;
        gap: var(--space-sm);
    }
}

@media (max-width: 480px) {
    .btn-large {
        padding: var(--space-md) var(--space-lg);
        font-size: 0.875rem;
    }

    .upload-area {
        padding: var(--space-lg);
    }

    .upload-icon {
        font-size: 3rem;
    }

    .upload-title {
        font-size: 1.25rem;
    }

    .feature-card {
        padding: var(--space-lg);
    }

    .feature-icon {
        font-size: 2rem;
    }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--space-xs) !important; }
.mb-2 { margin-bottom: var(--space-sm) !important; }
.mb-3 { margin-bottom: var(--space-md) !important; }
.mb-4 { margin-bottom: var(--space-lg) !important; }
.mb-5 { margin-bottom: var(--space-xl) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--space-xs) !important; }
.mt-2 { margin-top: var(--space-sm) !important; }
.mt-3 { margin-top: var(--space-md) !important; }
.mt-4 { margin-top: var(--space-lg) !important; }
.mt-5 { margin-top: var(--space-xl) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--space-xs) !important; }
.p-2 { padding: var(--space-sm) !important; }
.p-3 { padding: var(--space-md) !important; }
.p-4 { padding: var(--space-lg) !important; }
.p-5 { padding: var(--space-xl) !important; }

.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-1 { gap: var(--space-xs); }
.gap-2 { gap: var(--space-sm); }
.gap-3 { gap: var(--space-md); }
.gap-4 { gap: var(--space-lg); }
.gap-5 { gap: var(--space-xl); }

.w-full {
    width: 100%;
}

.h-full {
    height: 100%;
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-primary);
    border-top: 2px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
